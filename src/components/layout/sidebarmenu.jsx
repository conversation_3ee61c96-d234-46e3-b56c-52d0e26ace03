import { useNavigate } from "react-router-dom";

export default function SideBarMenu() {
  const pathSegments = window.location.pathname.split("/").filter(Boolean); // Splits and removes empty strings
  const currentPath = pathSegments[pathSegments.length - 1];
  const navigate = useNavigate();
  const handleNavigation = (route) => () => navigate(route);
  document.getElementById("main-wrapper").classList.add("show-sidebar");
  return (
    <>
      <div className="sidebarmenu">
        <div className="brand-logo d-flex align-items-center nav-logo">
          <a
            onClick={handleNavigation("/dashboard")}
            className="text-nowrap logo-img "
          >
            <img
              src="/src/assets/images/logos/logo.svg"
              alt="Logo"
              style={{ width: "80%" }}
            />
          </a>
        </div>
        <nav className="sidebar-nav " id="menu-right-mini-1" data-simplebar>
          <ul className="sidebar-menu" id="sidebarnav">
            <li className="nav-small-cap">
              <span className="hide-menu">Dashboards</span>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "dashboard" ? "active" : ""
                }`}
                onClick={handleNavigation("/dashboard")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:chart-line-duotone"></iconify-icon>
                <span className="hide-menu">Dashboard</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "account" ? "active" : ""
                }`}
                onClick={handleNavigation("/account")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:shield-user-line-duotone"></iconify-icon>
                <span className="hide-menu">Account Settings </span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "shop_profile" ? "active" : ""
                }`}
                onClick={handleNavigation("/shop_profile")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:shop-minimalistic-linear"></iconify-icon>
                <span className="hide-menu">Shop Profile</span>
              </a>
            </li>

            <li className="sidebar-item">
              <a
                className={`sidebar-link has-arrow ${
                  ["branch_list", "createBranch", "editBranch"].includes(
                    currentPath
                  )
                    ? "active"
                    : ""
                }`}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:map-point-linear"></iconify-icon>
                <span className="hide-menu">Branch Master</span>
              </a>
              <ul
                aria-expanded="false"
                className={`collapse first-level ${
                  ["branch_list", "createBranch", "editBranch"].includes(
                    currentPath
                  )
                    ? "in"
                    : ""
                }`}
              >
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["createBranch"].includes(currentPath) ? "active" : ""
                    }`}
                    onClick={handleNavigation("/createBranch")}
                  >
                    <span className="icon-small"></span>
                    Create Branch
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["branch_list", "editBranch"].includes(currentPath)
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation("/branch_list")}
                  >
                    <span className="icon-small"></span>
                    Branches
                  </a>
                </li>
              </ul>
            </li>
            <li className="sidebar-item">
              <a className="sidebar-link has-arrow" aria-expanded="false">
                <iconify-icon icon="solar:users-group-rounded-linear"></iconify-icon>
                <span className="hide-menu">Staff Master</span>
              </a>
              <ul
                aria-expanded="false"
                className={`collapse first-level 
                  ${
                    [
                      "roles",
                      "createRole",
                      "staffs",
                      "createStaff",
                      "editStaff",
                    ].includes(currentPath)
                      ? "in"
                      : ""
                  }`}
              >
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link 
                      ${
                        ["roles", "createRole"].includes(currentPath)
                          ? "active"
                          : ""
                      }`}
                    onClick={handleNavigation("/roles")}
                  >
                    <span className="icon-small"></span>
                    Staff Roles
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link 
                      ${["createStaff"].includes(currentPath) ? "active" : ""}`}
                    onClick={handleNavigation("/createStaff")}
                  >
                    <span className="icon-small"></span>
                    Create Staff
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link 
                      ${
                        ["staffs", "editStaff"].includes(currentPath)
                          ? "active"
                          : ""
                      }`}
                    onClick={handleNavigation("/staffs")}
                  >
                    <span className="icon-small"></span>
                    Staffs
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </nav>
        <nav className="sidebar-nav" id="menu-right-mini-2" data-simplebar>
          <ul className="sidebar-menu" id="sidebarnav">
            <li className="nav-small-cap">
              <span className="hide-menu">Product Master</span>
            </li>

            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "mainCategories" ? "active" : ""
                }`}
                onClick={handleNavigation("/mainCategories")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:folder-linear"></iconify-icon>
                <span className="hide-menu">Main Categories</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "subCategories" ? "active" : ""
                }`}
                onClick={handleNavigation("/subCategories")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:folder-open-linear"></iconify-icon>
                <span className="hide-menu">Sub Categories</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "childCategories" ? "active" : ""
                }`}
                onClick={handleNavigation("/childCategories")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:folder-path-connect-linear"></iconify-icon>
                <span className="hide-menu">Child Categories</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "attributes" ? "active" : ""
                }`}
                onClick={handleNavigation("/attributes")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:tag-linear"></iconify-icon>
                <span className="hide-menu">Attributes</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "attributesValues" ? "active" : ""
                }`}
                onClick={handleNavigation("/attributesValues")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:checklist-linear"></iconify-icon>
                <span className="hide-menu">Attributes Values</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "brands" ? "active" : ""
                }`}
                onClick={handleNavigation("/brands")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:bookmark-linear"></iconify-icon>
                <span className="hide-menu">Brands</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link has-arrow ${
                  [""].includes(currentPath) ? "active" : ""
                }`}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:box-linear"></iconify-icon>
                <span className="hide-menu">Products Master</span>
              </a>
              <ul
                aria-expanded="false"
                className={`collapse first-level ${
                  ["products", "createProduct", "editProduct"].includes(
                    currentPath
                  )
                    ? "in"
                    : ""
                }`}
              >
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["createProduct"].includes(currentPath) ? "active" : ""
                    }`}
                    onClick={handleNavigation("/createProduct")}
                  >
                    <span className="icon-small"></span>
                    Create Product
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["products", "editProduct"].includes(currentPath)
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation("/products")}
                  >
                    <span className="icon-small"></span>
                    All Products
                  </a>
                </li>
              </ul>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link has-arrow ${
                  [""].includes(currentPath) ? "active" : ""
                }`}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:box-minimalistic-linear"></iconify-icon>
                <span className="hide-menu">Inventory Master</span>
              </a>
              <ul
                aria-expanded="false"
                className={`collapse first-level ${
                  ["inventory", "createInventory", "editInventory", "inventoryHistory"].includes(
                    currentPath
                  )
                    ? "in"
                    : ""
                }`}
              >
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["createInventory"].includes(currentPath) ? "active" : ""
                    }`}
                    onClick={handleNavigation("/createInventory")}
                  >
                    <span className="icon-small"></span>
                    Add Inventory
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["inventory", "editInventory"].includes(currentPath)
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation("/inventory")}
                  >
                    <span className="icon-small"></span>
                    All Inventory
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["inventoryHistory"].includes(currentPath)
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation("/inventoryHistory")}
                  >
                    <span className="icon-small"></span>
                    Inventory History
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </nav>
        <nav className="sidebar-nav" id="menu-right-mini-3" data-simplebar>
          <ul className="sidebar-menu" id="sidebarnav">
            <li className="nav-small-cap">
              <span className="hide-menu">Customer Master</span>
            </li>

            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "customers" ? "active" : ""
                }`}
                onClick={handleNavigation("/customers")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:user-linear"></iconify-icon>
                <span className="hide-menu">Customers</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "customerContacts" ? "active" : ""
                }`}
                onClick={handleNavigation("/customerContacts")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:phone-linear"></iconify-icon>
                <span className="hide-menu">Contacts</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "customerAttachments" ? "active" : ""
                }`}
                onClick={handleNavigation("/customerAttachments")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:paperclip-linear"></iconify-icon>
                <span className="hide-menu">Attachments</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "customerNotes" ? "active" : ""
                }`}
                onClick={handleNavigation("/customerNotes")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:notes-linear"></iconify-icon>
                <span className="hide-menu">Notes</span>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </>
  );
}
